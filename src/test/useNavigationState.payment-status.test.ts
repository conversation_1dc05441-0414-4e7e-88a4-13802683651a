import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useNavigationState } from '../hooks/useNavigationState';
import { supabase } from '../lib/supabase';

// Mock Supabase
vi.mock('../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({}))
      }))
    }))
  }
}));

// Mock certificate context
vi.mock('../contexts/CertificateContext', () => ({
  useCertificate: () => ({
    activeCertificateId: 'test-cert-id'
  })
}));

describe('useNavigationState - Payment Status Handling', () => {
  const mockSupabase = supabase as any;
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should mark all pages as completed when certificate is in paid status', async () => {
    // Mock certificate with paid status
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'paid' },
      error: null
    });

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Check that all pages including zusammenfassung are marked as completed
    expect(result.current.isPageCompleted('objektdaten', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('gebaeudedetails1', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('gebaeudedetails2', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('verbrauch', 'zusammenfassung')).toBe(true);
    // This is the key fix: zusammenfassung itself should be marked as completed for paid certificates
    expect(result.current.isPageCompleted('zusammenfassung', 'zusammenfassung')).toBe(true);
  });

  it('should mark all pages as completed when certificate is in payment_initiated status', async () => {
    // Mock certificate with payment_initiated status
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'payment_initiated' },
      error: null
    });
    
    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/B'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // Check that all pages before zusammenfassung are marked as completed for WG/B type
    expect(result.current.isPageCompleted('objektdaten', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('gebaeudedetails1', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('gebaeudedetails2', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('fenster', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('heizung', 'zusammenfassung')).toBe(true);
    expect(result.current.isPageCompleted('tww-lueftung', 'zusammenfassung')).toBe(true);
  });

  it('should treat payment status as zusammenfassung page for navigation', async () => {
    // Mock certificate with paid status
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'paid' },
      error: null
    });
    
    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // The navigation state should treat payment status as zusammenfassung
    expect(result.current.navigationState?.currentPage).toBe('zusammenfassung');
    expect(result.current.navigationState?.actualDbStatus).toBe('paid');
  });

  it('should mark all previous pages as visited when in payment status', async () => {
    // Mock certificate with payment_failed status
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'payment_failed' },
      error: null
    });
    
    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // All pages before zusammenfassung should be in visitedPages
    const visitedPages = result.current.navigationState?.visitedPages || [];
    expect(visitedPages).toContain('objektdaten');
    expect(visitedPages).toContain('gebaeudedetails1');
    expect(visitedPages).toContain('gebaeudedetails2');
    expect(visitedPages).toContain('verbrauch');
    expect(visitedPages).not.toContain('zusammenfassung'); // Current page is not in visited
  });

  it('should make zusammenfassung accessible when navigating from other pages in paid status', async () => {
    // Mock certificate with paid status
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'paid' },
      error: null
    });

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for the hook to initialize
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
    });

    // When navigating from objektdaten page, zusammenfassung should be accessible and completed
    expect(result.current.isPageAccessible('zusammenfassung', 'objektdaten')).toBe(true);
    expect(result.current.isPageCompleted('zusammenfassung', 'objektdaten')).toBe(true);

    // When navigating from any other page, zusammenfassung should be accessible and completed
    expect(result.current.isPageAccessible('zusammenfassung', 'gebaeudedetails1')).toBe(true);
    expect(result.current.isPageCompleted('zusammenfassung', 'gebaeudedetails1')).toBe(true);

    expect(result.current.isPageAccessible('zusammenfassung', 'verbrauch')).toBe(true);
    expect(result.current.isPageCompleted('zusammenfassung', 'verbrauch')).toBe(true);
  });
});
